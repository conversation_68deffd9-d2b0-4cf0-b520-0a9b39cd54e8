bl_info = {
    "name": "<PERSON>H-Scatter",
    "author": "<PERSON><PERSON><PERSON>",
    "version": (1, 5),
    "blender": (4, 00, 0),
    "location": "OUTLINER > HT",
    "description": "",
    "warning": "",
    "doc_url": "",
    "category": "KH",
}

import bpy
import os
from bpy.utils import register_class, unregister_class

# دالة مساعدة للبحث عن إعدادات الإضافة
def get_addon_preferences(context, preference_name):
    """
    البحث عن إعدادات الإضافة في المسارات المختلفة
    """
    try:
        # محاولة البحث بالاسم الافتراضي
        addon = context.preferences.addons['KH-Tools']
        if hasattr(addon.preferences, preference_name):
            return getattr(addon.preferences, preference_name)
    except KeyError:
        try:
            # محاولة البحث باسم المجل<PERSON> الحالي
            addon = context.preferences.addons['kh_tools']
            if hasattr(addon.preferences, preference_name):
                return getattr(addon.preferences, preference_name)
        except KeyError:
            try:
                # البحث في جميع الإضافات المحملة
                for addon_name in context.preferences.addons.keys():
                    if 'kh' in addon_name.lower() or 'tools' in addon_name.lower():
                        addon = context.preferences.addons[addon_name]
                        if hasattr(addon.preferences, preference_name):
                            return getattr(addon.preferences, preference_name)
            except:
                pass
    # إرجاع True كقيمة افتراضية إذا لم يتم العثور على الإعداد
    return True

def KH_Scatter_collection():
    collections_names = ["KH-Scatter"]
    scene = bpy.context.scene
    existing_collections = [collection.name for collection in bpy.data.collections]
    for name in collections_names:
        if name not in existing_collections:
            new_collection = bpy.data.collections.new(name)
            scene.collection.children.link(new_collection)


class kh_scatter_Operator1(bpy.types.Operator):
    bl_idname = "object.import_particle_settings1"
    bl_label = "Import Particle Settings"
    
    def execute(self, context):
        KH_Scatter_collection()
        source_file = os.path.join(os.path.dirname(__file__), "scatter.blend")
        particle_settings_name = "KH-Grass"

        with bpy.data.libraries.load(source_file, link=False) as (data_from, data_to):
            if particle_settings_name in data_from.particles:
                data_to.particles = [particle_settings_name] 
                
        if particle_settings_name in bpy.data.particles:
            if particle_settings_name not in bpy.data.collections:
                bpy.ops.wm.append(directory=source_file + "\\Collection\\", filename=particle_settings_name, link=False)
        
        if "KH-Grass" in bpy.data.collections and "KH-Scatter" in bpy.data.collections:
            kh_rock = bpy.data.collections["KH-Grass"]
            kh_scatter = bpy.data.collections["KH-Scatter"]
            
            if kh_rock.name in bpy.context.scene.collection.children:
                bpy.context.scene.collection.children.unlink(kh_rock)
            for collection in bpy.data.collections:
                if kh_rock.name in collection.children:
                    collection.children.unlink(kh_rock)
            kh_scatter.children.link(kh_rock)
            
        if particle_settings_name in bpy.data.particles:
            target_object = bpy.context.active_object 
            bpy.ops.object.particle_system_add()
            particle_system = target_object.particle_systems[-1]            
            particle_system.settings = bpy.data.particles[particle_settings_name]
            particle_system.name = "KH-Grass"
            # إنشاء نسخة منفصلة من إعدادات النظام الجزيئي
            particle_system.settings = particle_system.settings.copy()
            
            bpy.ops.outliner.orphans_purge(do_recursive=True)
            
            bpy.context.view_layer.update()
            
            particle_system.settings.instance_collection = bpy.data.collections[particle_settings_name]
            
        kh_rock_name = "KH-Grass"
        kh_scatter_name = "KH-Scatter"

        kh_rock = bpy.data.collections.get(kh_rock_name)
        kh_scatter = bpy.data.collections.get(kh_scatter_name)

        if kh_rock and kh_scatter:
            view_layer = bpy.context.view_layer
            if kh_rock.name in view_layer.layer_collection.children:
                view_layer.layer_collection.children[kh_rock.name].exclude = True
            if kh_scatter_name in view_layer.layer_collection.children:
                scatter_collection = view_layer.layer_collection.children[kh_scatter_name]
                for child_collection in scatter_collection.children:
                    if child_collection.collection.name == kh_rock_name:
                        child_collection.exclude = True

           
        bpy.app.handlers.depsgraph_update_post.append(scale_Grass_handler)
        # with bpy.data.libraries.load(source_file) as (data_from, data_to):
        #     data_to.collections = ["KH-Scatter1"]
        # for collection in data_to.collections:
        #     bpy.context.collection.children.link(collection)
        #     bpy.ops.outliner.orphans_purge(do_recursive=True)
        
        return {'FINISHED'}
    

collection_name = "KH-Grass"
prev_scale_z = {}
def scale_Grass_handler(scene):
    global prev_scale_z
    collection = bpy.data.collections.get(collection_name)
    if collection:
        for obj in collection.all_objects:
            if obj.type == 'MESH':
                if obj.name not in prev_scale_z:
                    prev_scale_z[obj.name] = None
                if prev_scale_z[obj.name] is None or obj.scale.z != prev_scale_z[obj.name]:
                    obj.scale.x = obj.scale.z
                    obj.scale.y = obj.scale.z
                    prev_scale_z[obj.name] = obj.scale.z

            
class kh_KH_Grass1_Operator1(bpy.types.Operator):
    bl_idname = "object.import_kh_grass1"
    bl_label = "Import Particle Settings"
    
    def execute(self, context):
        KH_Scatter_collection()
        source_file = os.path.join(os.path.dirname(__file__), "scatter.blend")
        particle_settings_name = "KH-Grass1"

        with bpy.data.libraries.load(source_file, link=False) as (data_from, data_to):
            if particle_settings_name in data_from.particles:
                data_to.particles = [particle_settings_name] 
                
        if particle_settings_name in bpy.data.particles:
            if particle_settings_name not in bpy.data.collections:
                bpy.ops.wm.append(directory=source_file + "\\Collection\\", filename=particle_settings_name, link=False)
        
        if "KH-Grass1" in bpy.data.collections and "KH-Scatter" in bpy.data.collections:
            kh_rock = bpy.data.collections["KH-Grass1"]
            kh_scatter = bpy.data.collections["KH-Scatter"]
            
            if kh_rock.name in bpy.context.scene.collection.children:
                bpy.context.scene.collection.children.unlink(kh_rock)
            for collection in bpy.data.collections:
                if kh_rock.name in collection.children:
                    collection.children.unlink(kh_rock)
            kh_scatter.children.link(kh_rock)
                
        if particle_settings_name in bpy.data.particles:
            target_object = bpy.context.active_object 
            bpy.ops.object.particle_system_add()
            particle_system = target_object.particle_systems[-1]
            particle_system.settings = bpy.data.particles[particle_settings_name]
            bpy.ops.outliner.orphans_purge(do_recursive=True)
            particle_system.name = "KH-Grass1"
            # إنشاء نسخة منفصلة من إعدادات النظام الجزيئي
            particle_system.settings = particle_system.settings.copy()
            bpy.context.view_layer.update()
            
            particle_system.settings.instance_collection = bpy.data.collections[particle_settings_name]
                   
        kh_rock_name = "KH-Grass1"
        kh_scatter_name = "KH-Scatter"

        kh_rock = bpy.data.collections.get(kh_rock_name)
        kh_scatter = bpy.data.collections.get(kh_scatter_name)

        if kh_rock and kh_scatter:
            view_layer = bpy.context.view_layer
            if kh_rock.name in view_layer.layer_collection.children:
                view_layer.layer_collection.children[kh_rock.name].exclude = True
            if kh_scatter_name in view_layer.layer_collection.children:
                scatter_collection = view_layer.layer_collection.children[kh_scatter_name]
                for child_collection in scatter_collection.children:
                    if child_collection.collection.name == kh_rock_name:
                        child_collection.exclude = True
                        
        bpy.app.handlers.depsgraph_update_post.append(scale_Grass1_handler)
        return {'FINISHED'}

collection_name1 = "KH-Grass1"
prev_scale_z1 = {}
def scale_Grass1_handler(scene):
    global prev_scale_z1
    collection = bpy.data.collections.get(collection_name1)
    if collection:
        for obj in collection.all_objects:
            if obj.type == 'MESH':
                if obj.name not in prev_scale_z1:
                    prev_scale_z1[obj.name] = None
                if prev_scale_z1[obj.name] is None or obj.scale.z != prev_scale_z1[obj.name]:
                    obj.scale.x = obj.scale.z
                    obj.scale.y = obj.scale.z
                    prev_scale_z1[obj.name] = obj.scale.z
                    
class kh_scatter_Operator2(bpy.types.Operator):
    bl_idname = "object.import_particle_settings2"
    bl_label = "Import Particle Settings"
    
    def execute(self, context):
        KH_Scatter_collection()
        source_file = os.path.join(os.path.dirname(__file__), "scatter.blend")

        particle_settings_name = "KH-Leaves"

        with bpy.data.libraries.load(source_file, link=False) as (data_from, data_to):
            if particle_settings_name in data_from.particles:
                data_to.particles = [particle_settings_name]
         # Append the collection with the same name as the particle system
        if particle_settings_name in bpy.data.particles:
            if particle_settings_name not in bpy.data.collections:
                bpy.ops.wm.append(directory=source_file + "\\Collection\\", filename=particle_settings_name, link=False)
            
        if "KH-Leaves" in bpy.data.collections and "KH-Scatter" in bpy.data.collections:
            kh_rock = bpy.data.collections["KH-Leaves"]
            kh_scatter = bpy.data.collections["KH-Scatter"]
            
            if kh_rock.name in bpy.context.scene.collection.children:
                bpy.context.scene.collection.children.unlink(kh_rock)
            for collection in bpy.data.collections:
                if kh_rock.name in collection.children:
                    collection.children.unlink(kh_rock)
            kh_scatter.children.link(kh_rock)
                
        if particle_settings_name in bpy.data.particles:
            target_object = bpy.context.active_object 
            bpy.ops.object.particle_system_add()
            particle_system = target_object.particle_systems[-1]
            particle_system.settings = bpy.data.particles[particle_settings_name]

            bpy.ops.outliner.orphans_purge(do_recursive=True)
            particle_system.name = "KH-Leaves"
            # إنشاء نسخة منفصلة من إعدادات النظام الجزيئي
            particle_system.settings = particle_system.settings.copy()
            bpy.context.view_layer.update()
            
            particle_system.settings.instance_collection = bpy.data.collections[particle_settings_name]
        
        kh_rock_name = "KH-Leaves"
        kh_scatter_name = "KH-Scatter"

        kh_rock = bpy.data.collections.get(kh_rock_name)
        kh_scatter = bpy.data.collections.get(kh_scatter_name)

        if kh_rock and kh_scatter:
            view_layer = bpy.context.view_layer
            if kh_rock.name in view_layer.layer_collection.children:
                view_layer.layer_collection.children[kh_rock.name].exclude = True
            if kh_scatter_name in view_layer.layer_collection.children:
                scatter_collection = view_layer.layer_collection.children[kh_scatter_name]
                for child_collection in scatter_collection.children:
                    if child_collection.collection.name == kh_rock_name:
                        child_collection.exclude = True
        bpy.app.handlers.depsgraph_update_post.append(scale_Leaves_handler)  
        return {'FINISHED'}

collection_name2 = "KH-Leaves"
prev_scale_z2 = {}
def scale_Leaves_handler(scene):
    global prev_scale_z2
    collection = bpy.data.collections.get(collection_name2)
    if collection:
        for obj in collection.all_objects:
            if obj.type == 'MESH':
                if obj.name not in prev_scale_z2:
                    prev_scale_z2[obj.name] = None
                if prev_scale_z2[obj.name] is None or obj.scale.z != prev_scale_z2[obj.name]:
                    obj.scale.x = obj.scale.z
                    obj.scale.y = obj.scale.z
                    prev_scale_z2[obj.name] = obj.scale.z
                    
class kh_scatter_Operator3(bpy.types.Operator):
    bl_idname = "object.import_particle_settings3"
    bl_label = "Import Particle Settings"
    
    def execute(self, context):
        KH_Scatter_collection()
        source_file = os.path.join(os.path.dirname(__file__), "scatter.blend")
        particle_settings_name = "KH-Ivy Wall"

        with bpy.data.libraries.load(source_file, link=False) as (data_from, data_to):
            if particle_settings_name in data_from.particles:
                data_to.particles = [particle_settings_name] 
                
        if particle_settings_name in bpy.data.particles:
            if particle_settings_name not in bpy.data.collections:
                bpy.ops.wm.append(directory=source_file + "\\Collection\\", filename=particle_settings_name, link=False)
        
        if "KH-Ivy Wall" in bpy.data.collections and "KH-Scatter" in bpy.data.collections:
            kh_rock = bpy.data.collections["KH-Ivy Wall"]
            kh_scatter = bpy.data.collections["KH-Scatter"]
            
            if kh_rock.name in bpy.context.scene.collection.children:
                bpy.context.scene.collection.children.unlink(kh_rock)
            for collection in bpy.data.collections:
                if kh_rock.name in collection.children:
                    collection.children.unlink(kh_rock)
            kh_scatter.children.link(kh_rock)
                 
        if particle_settings_name in bpy.data.particles:
            target_object = bpy.context.active_object 
            bpy.ops.object.particle_system_add()
            particle_system = target_object.particle_systems[-1]
            particle_system.settings = bpy.data.particles[particle_settings_name]
            bpy.ops.outliner.orphans_purge(do_recursive=True)
            particle_system.name = "KH-Ivy Wall"
            particle_system.settings = particle_system.settings.copy()
            bpy.context.view_layer.update()
            
            particle_system.settings.instance_collection = bpy.data.collections[particle_settings_name]
   
        kh_rock_name = "KH-Ivy Wall"
        kh_scatter_name = "KH-Scatter"

        kh_rock = bpy.data.collections.get(kh_rock_name)
        kh_scatter = bpy.data.collections.get(kh_scatter_name)

        if kh_rock and kh_scatter:
            view_layer = bpy.context.view_layer
            if kh_rock.name in view_layer.layer_collection.children:
                view_layer.layer_collection.children[kh_rock.name].exclude = True
            if kh_scatter_name in view_layer.layer_collection.children:
                scatter_collection = view_layer.layer_collection.children[kh_scatter_name]
                for child_collection in scatter_collection.children:
                    if child_collection.collection.name == kh_rock_name:
                        child_collection.exclude = True
        bpy.app.handlers.depsgraph_update_post.append(scale_Ivy_handler)
        return {'FINISHED'}

collection_name3 = "KH-Ivy Wall"
prev_scale_z3 = {}
def scale_Ivy_handler(scene):
    global prev_scale_z3
    collection = bpy.data.collections.get(collection_name3)
    if collection:
        for obj in collection.all_objects:
            if obj.type == 'MESH':
                if obj.name not in prev_scale_z3:
                    prev_scale_z3[obj.name] = None
                if prev_scale_z3[obj.name] is None or obj.scale.z != prev_scale_z3[obj.name]:
                    obj.scale.x = obj.scale.z
                    obj.scale.y = obj.scale.z
                    prev_scale_z3[obj.name] = obj.scale.z
                      
class kh_scatter_Operator4(bpy.types.Operator):
    bl_idname = "object.import_particle_settings4"
    bl_label = "Import Particle Settings"
    
    def execute(self, context):
        KH_Scatter_collection()
        source_file = os.path.join(os.path.dirname(__file__), "scatter.blend")
        particle_settings_name = "KH-ALL MIX"

        with bpy.data.libraries.load(source_file, link=False) as (data_from, data_to):
            if particle_settings_name in data_from.particles:
                data_to.particles = [particle_settings_name] 
                
        if particle_settings_name in bpy.data.particles:
            if particle_settings_name not in bpy.data.collections:
                bpy.ops.wm.append(directory=source_file + "\\Collection\\", filename=particle_settings_name, link=False)
         
        if "KH-ALL MIX" in bpy.data.collections and "KH-Scatter" in bpy.data.collections:
            kh_rock = bpy.data.collections["KH-ALL MIX"]
            kh_scatter = bpy.data.collections["KH-Scatter"]
            
            if kh_rock.name in bpy.context.scene.collection.children:
                bpy.context.scene.collection.children.unlink(kh_rock)
            for collection in bpy.data.collections:
                if kh_rock.name in collection.children:
                    collection.children.unlink(kh_rock)
            kh_scatter.children.link(kh_rock)
               
        if particle_settings_name in bpy.data.particles:
            target_object = bpy.context.active_object 
            bpy.ops.object.particle_system_add()
            particle_system = target_object.particle_systems[-1]
            particle_system.settings = bpy.data.particles[particle_settings_name]
            bpy.ops.outliner.orphans_purge(do_recursive=True)
            particle_system.name = "KH-ALL MIX"
            particle_system.settings = particle_system.settings.copy()
            bpy.context.view_layer.update()
            
            particle_system.settings.instance_collection = bpy.data.collections[particle_settings_name]
        
        kh_rock_name = "KH-ALL MIX"
        kh_scatter_name = "KH-Scatter"

        kh_rock = bpy.data.collections.get(kh_rock_name)
        kh_scatter = bpy.data.collections.get(kh_scatter_name)

        if kh_rock and kh_scatter:
            view_layer = bpy.context.view_layer
            if kh_rock.name in view_layer.layer_collection.children:
                view_layer.layer_collection.children[kh_rock.name].exclude = True
            if kh_scatter_name in view_layer.layer_collection.children:
                scatter_collection = view_layer.layer_collection.children[kh_scatter_name]
                for child_collection in scatter_collection.children:
                    if child_collection.collection.name == kh_rock_name:
                        child_collection.exclude = True
        bpy.app.handlers.depsgraph_update_post.append(scale_MIX_handler)
        return {'FINISHED'}
    
collection_name4 = "KH-ALL MIX"
prev_scale_z4 = {}
def scale_MIX_handler(scene):
    global prev_scale_z4
    collection = bpy.data.collections.get(collection_name4)
    if collection:
        for obj in collection.all_objects:
            if obj.type == 'MESH':
                if obj.name not in prev_scale_z4:
                    prev_scale_z4[obj.name] = None
                if prev_scale_z4[obj.name] is None or obj.scale.z != prev_scale_z4[obj.name]:
                    obj.scale.x = obj.scale.z
                    obj.scale.y = obj.scale.z
                    prev_scale_z4[obj.name] = obj.scale.z
                    
class kh_scatter_Operator5(bpy.types.Operator):
    bl_idname = "object.import_particle_settings5"
    bl_label = "Import Particle Settings"
    
    def execute(self, context):
        KH_Scatter_collection()
        source_file = os.path.join(os.path.dirname(__file__), "scatter.blend")
        particle_settings_name = "KH-Rock"

        with bpy.data.libraries.load(source_file, link=False) as (data_from, data_to):
            if particle_settings_name in data_from.particles:
                data_to.particles = [particle_settings_name]
        
        if particle_settings_name in bpy.data.particles:
            if particle_settings_name not in bpy.data.collections:
                bpy.ops.wm.append(directory=source_file + "\\Collection\\", filename=particle_settings_name, link=False)
                
        if "KH-Rock" in bpy.data.collections and "KH-Scatter" in bpy.data.collections:
            kh_rock = bpy.data.collections["KH-Rock"]
            kh_scatter = bpy.data.collections["KH-Scatter"]
            
            if kh_rock.name in bpy.context.scene.collection.children:
                bpy.context.scene.collection.children.unlink(kh_rock)
            for collection in bpy.data.collections:
                if kh_rock.name in collection.children:
                    collection.children.unlink(kh_rock)
            kh_scatter.children.link(kh_rock)
            
              
        if particle_settings_name in bpy.data.particles:
            target_object = bpy.context.active_object 
            bpy.ops.object.particle_system_add()
            particle_system = target_object.particle_systems[-1]
            particle_system.settings = bpy.data.particles[particle_settings_name]
            bpy.ops.outliner.orphans_purge(do_recursive=True)
            particle_system.name = "KH-Rock"
            particle_system.settings = particle_system.settings.copy()
            bpy.context.view_layer.update()
            
            particle_system.settings.instance_collection = bpy.data.collections[particle_settings_name]
            # kh_leaves = bpy.data.collections[particle_settings_name]
            # view_layer = bpy.context.view_layer
            # view_layer.layer_collection.children[kh_leaves.name].exclude = True

        kh_rock_name = "KH-Rock"
        kh_scatter_name = "KH-Scatter"

        kh_rock = bpy.data.collections.get(kh_rock_name)
        kh_scatter = bpy.data.collections.get(kh_scatter_name)

        if kh_rock and kh_scatter:
            view_layer = bpy.context.view_layer
            if kh_rock.name in view_layer.layer_collection.children:
                view_layer.layer_collection.children[kh_rock.name].exclude = True
            if kh_scatter_name in view_layer.layer_collection.children:
                scatter_collection = view_layer.layer_collection.children[kh_scatter_name]
                for child_collection in scatter_collection.children:
                    if child_collection.collection.name == kh_rock_name:
                        child_collection.exclude = True
        bpy.app.handlers.depsgraph_update_post.append(scale_Rock_handler)
        return {'FINISHED'}
    
collection_name5 = "KH-Rock"
prev_scale_z5 = {}
def scale_Rock_handler(scene):
    global prev_scale_z5
    collection = bpy.data.collections.get(collection_name5)
    if collection:
        for obj in collection.all_objects:
            if obj.type == 'MESH':
                if obj.name not in prev_scale_z5:
                    prev_scale_z5[obj.name] = None
                if prev_scale_z5[obj.name] is None or obj.scale.z != prev_scale_z5[obj.name]:
                    obj.scale.x = obj.scale.z
                    obj.scale.y = obj.scale.z
                    prev_scale_z5[obj.name] = obj.scale.z
                    
class kh_scatter_fur(bpy.types.Operator):
    bl_idname = "object.fur"
    bl_label = "Import Particle Settings"
    
    def execute(self, context):
        source_file = os.path.join(os.path.dirname(__file__), "scatter.blend")
        particle_settings_name = "KH-FUR"

        with bpy.data.libraries.load(source_file, link=False) as (data_from, data_to):
            if particle_settings_name in data_from.particles:
                data_to.particles = [particle_settings_name]  
        if particle_settings_name in bpy.data.particles:
            target_object = bpy.context.active_object 
            bpy.ops.object.particle_system_add()
            particle_system = target_object.particle_systems[-1]
            particle_system.settings = bpy.data.particles[particle_settings_name]
            bpy.ops.outliner.orphans_purge(do_recursive=True)
            particle_system.name = "KH-FUR"
            particle_system.settings = particle_system.settings.copy()
            bpy.context.view_layer.update()
        
        # with bpy.data.libraries.load(source_file) as (data_from, data_to):
        #     data_to.collections = ["KH-Scatter1"]
        # for collection in data_to.collections:
        #     bpy.context.collection.children.link(collection)
        #     bpy.ops.outliner.orphans_purge(do_recursive=True)
        
        return {'FINISHED'}
    
class kh_show_viewporh(bpy.types.Operator):
    bl_idname = "object.kh_show_viewporh"
    bl_label = "show_viewpor"
    
    def execute(self, context):
        objects = bpy.data.objects
        for obj in objects:
            if obj.modifiers:
                for modifier in obj.modifiers:
                    if modifier.type == 'PARTICLE_SYSTEM':
                        modifier.show_viewport = False
        return {'FINISHED'}
    
class kh_show_viewpor(bpy.types.Operator):
    bl_idname = "object.kh_show_viewpor"
    bl_label = "show_viewpor"
    def execute(self, context):
        objects = bpy.data.objects
        for obj in objects:
            if obj.modifiers:
                for modifier in obj.modifiers:
                    if modifier.type == 'PARTICLE_SYSTEM':
                        modifier.show_viewport = True
        return {'FINISHED'}
    
    

class kh_scatter_Panel(bpy.types.Panel):
    bl_label = ""
    bl_idname = "KH_PT_ParticleSystemPanel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'KH-Tools'
    bl_options = {'DEFAULT_CLOSED'}
    
    @classmethod
    def poll(cls, context):
        # استخدام الدالة المساعدة للبحث عن إعدادات الإضافة
        return get_addon_preferences(context, 'KH_Scatter') == True

    def draw_header(self, context: bpy.types.Context):
        layout = self.layout
        try:
            #self.layout.label(text="", icon="OUTLINER_OB_CURVES")
            layout.label(text="Scatter", icon="OUTLINER_OB_CURVES")
        except KeyError:
            pass
        objects = bpy.data.objects
        found = False  # علم لتحديد ما إذا تم العثور على كائن مناسب

#        for obj in objects:
#            if obj.type == 'MESH' and obj.modifiers:
#                for modifier in obj.modifiers:
#                    if modifier.type == 'PARTICLE_SYSTEM':
#                        if modifier.show_viewport == True:
#                            layout.operator("object.kh_show_viewporh", text="", icon="RESTRICT_VIEW_OFF")
#                        else:
#                            layout.operator("object.kh_show_viewpor", text="", icon="RESTRICT_VIEW_ON")
#                        
#                        found = True  # تم العثور على الكائن المناسب
#                        break  # كسر الحلقة الداخلية
#                if found:
#                    break  # كسر الحلقة الخارجية
                
        
        for obj in objects:
            if obj.type == 'MESH' and obj.modifiers:
                for modifier in obj.modifiers:
                    if modifier.type == 'PARTICLE_SYSTEM' and modifier.show_viewport:
                        # إذا كان هناك نظام جسيمات ظاهر في الفيوبورت
                        layout.operator("object.kh_show_viewporh", text="", icon="RESTRICT_VIEW_OFF")
                        found = True  # تم العثور على الكائن المناسب
                        break  # كسر الحلقة الداخلية بمجرد العثور على الكائن المناسب
                if found:
                    break  # كسر الحلقة الخارجية إذا تم العثور على كائن مناسب

        if not found:
            # إذا لم يتم العثور على أي كائن يحتوي على نظام جسيمات ظاهر
            layout.operator("object.kh_show_viewpor", text="", icon="RESTRICT_VIEW_ON")

       

#    @classmethod
#    def poll(cls, context):
#        return context.object and context.object.type == 'MESH'

    def draw(self, context):
        try:
        #self.layout.label(text="", icon="OUTLINER_OB_CURVES")
            objects = bpy.data.objects
            for obj in objects:
                if obj.type == 'MESH' and obj.modifiers:
                    for modifier in obj.modifiers:
                        if modifier.type == 'PARTICLE_SYSTEM':
                            layout = self.layout
                            box = layout.box()
                            row = box.row()
                            row.label(text="Viewport Display", icon="MEMORY")
                            if bpy.context.scene.render.use_simplify == False:
                                row.prop(context.scene.render, "use_simplify", text="") 
                            # row.prop(context.scene.render, "simplify_child_particles", text="")
                            else:
                                row.prop(context.scene.render, "simplify_child_particles", text="")
                    break
        except KeyError:
            pass                        
                                                    
        layout = self.layout
        obj = context.object
        if context.object and context.object.type == 'MESH':
            if obj.particle_systems:
                for psys in obj.particle_systems:
                    box = layout.box()
                    row = box.row()
                    #row.label(text=psys.name)
                                    
                    for ps_idx1, ps in enumerate(obj.particle_systems):
                        for modifier in obj.modifiers:
                            if modifier.type == 'PARTICLE_SYSTEM' and modifier.particle_system == psys:
                                #row.prop(modifier, "show_viewport", text="")
                                #row.prop(modifier, "show_render", text="")
                                break
                                        

                #top_panel = layout.box()
            
                for ps_idx, ps in enumerate(obj.particle_systems):
                    #row = top_panel.row(align=True)
                    row.prop(ps, "name", text="", emboss=False)
                    #row.operator("object.select_particle_system", text="", icon='RESTRICT_SELECT_OFF').particle_system_index = ps_idx
        
                    for psys in obj.particle_systems:
                        for modifier in obj.modifiers:
                            if modifier.type == 'PARTICLE_SYSTEM' and modifier.particle_system == psys:
                                row.prop(modifier, "show_viewport", text="")
                                row.prop(modifier, "show_render", text="")
                                break
                    break
                row.operator("object.particle_delete", text="", icon='TRASH').particle_system_index1 = ps_idx1
                row=box.row()
                row.template_ID(psys, "settings", new="particle.new")
                # عرض قائمة use_collection_count كاملة
                # for ps in context.object.particle_systems:
                #     box = layout.box()
                #     if ps.settings.use_collection_count:
                #         for particle_weight in ps.settings.instance_weights:
                #             row = box.row()
                #             row.prop(particle_weight, "count", text=particle_weight.name)
                #         break

            # Bottom panel to display particle system settings
                bottom_panel = layout.box()
            

                selected_ps = obj.particle_systems[context.scene.selected_particle_system]
                bottom_panel.label(text="Settings:",icon= "SETTINGS")
                #bottom_panel.template_ID(psys, "settings", new="particle.new")
                bottom_panel.prop(selected_ps.settings, "count", text="Distribution")
                bottom_panel.prop(selected_ps.settings, "hair_length", text="Scale")
                #bottom_panel.prop(selected_ps.settings, "particle_size", text="Scale")
                bottom_panel.prop(selected_ps.settings, "size_random", text="S-Random")
                bottom_panel.prop(selected_ps.settings, "phase_factor_random", text="R-Random")
                #particle_settings_name = "KH-Scatter1"
                #for particle_settings_name in bpy.data.particles:
                    #if particle_settings_name in bpy.data.particles:
                target_object = bpy.context.active_object 
                if target_object is not None:
                    if hasattr(target_object, 'particle_systems') and target_object.particle_systems:
                        particle_system = target_object.particle_systems[-1]
                        bottom_panel.prop(particle_system, "seed", text="Seed")
 
            else:
                layout.operator("object.import_particle_settings1", text="Grass",icon="OUTLINER_OB_CURVES")
                layout.operator("object.import_kh_grass1", text="Grass1",icon="OUTLINER_OB_CURVES")
                layout.operator("object.import_particle_settings2", text="Leaves",icon="OUTLINER_OB_CURVES")
                layout.operator("object.import_particle_settings3", text="Ivy Wall",icon="OUTLINER_OB_CURVES")
                layout.operator("object.import_particle_settings5", text="Rock",icon="OUTLINER_OB_POINTCLOUD")
                #layout.operator("object.import_particle_settings4", text="ALL MIX",icon="BRUSHES_ALL")
                layout.operator("object.fur", text="FUR",icon="PARTICLES")
        else:
            layout.label(text="Select your object first !",icon="QUESTION")
            
class kh_scatter_Asset_LIST_Panel(bpy.types.Panel):
    bl_label = ""
    bl_idname = "KH_PT_Particle_asset_list_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'KH-Tools'
    bl_options = {'DEFAULT_CLOSED'}
    bl_parent_id   = "KH_PT_ParticleSystemPanel"
    
    @classmethod
    def poll(cls, context):
        # استخدام الدالة المساعدة للبحث عن إعدادات الإضافة
        return get_addon_preferences(context, 'KH_Scatter') == True

    def draw_header(self, context: bpy.types.Context):
        try:
            layout = self.layout
            layout.label(text="Asset LIST", icon="ASSET_MANAGER")

        except KeyError:
            pass

    def draw(self, context):                 
                                                    
        layout = self.layout
        obj = context.object
        if context.object and context.object.type == 'MESH':
            if obj.particle_systems:                 
                for ps in context.object.particle_systems:
                    box = layout.box()
                    if ps.settings.use_collection_count:
                        for particle_weight in ps.settings.instance_weights:
                            row = box.row()
                            particle_name_without_suffix = particle_weight.name.split(':')[0]
                            row.prop(particle_weight, "count", text=particle_name_without_suffix)
                            
                            objects = bpy.data.objects

                            for obj in objects:
                                if obj.type == 'MESH':
                                    obj_name_without_suffix = obj.name.split(':')[0]  
                                    particle_name_without_suffix = particle_weight.name.split(':')[0] 
                                    if obj_name_without_suffix == particle_name_without_suffix:
                                        row.prop(obj, "scale",index=2, text="Scale")
                                        break

          
            
  
class ParticleSystemSelectOperator(bpy.types.Operator):
    bl_idname = "object.select_particle_system"
    bl_label = "Select Particle System"

    particle_system_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.selected_particle_system = self.particle_system_index
        return {'FINISHED'}  

class ParticleDeleteOperator(bpy.types.Operator):
    bl_idname = "object.particle_delete"
    bl_label = "Delete Particle System"
    particle_system_index1: bpy.props.IntProperty()

    def execute(self, context):
        obj = context.object
        if obj and obj.particle_systems:
            particle_system_index1= self.particle_system_index1
            if 0 <= particle_system_index1 < len(obj.particle_systems):
                bpy.ops.object.particle_system_remove()
                #obj.particle_systems.remove(obj.particle_systems[particle_system_index])
                bpy.ops.outliner.orphans_purge(do_recursive=True)
                
        return {'FINISHED'}        



classes = ( kh_scatter_Panel,
            kh_scatter_Asset_LIST_Panel,
            kh_scatter_Operator1,
            kh_KH_Grass1_Operator1,
            kh_scatter_Operator2,
            kh_scatter_Operator3,
            kh_scatter_Operator4,
            kh_scatter_Operator5,
            ParticleDeleteOperator,
            ParticleSystemSelectOperator,
            kh_show_viewporh,
            kh_show_viewpor,
            kh_scatter_fur,           
                )

def register():
    for i in classes:
        register_class(i)
    bpy.types.Scene.selected_particle_system = bpy.props.IntProperty()   


def unregister():
    for i in classes:
        unregister_class(i)
    # التحقق من وجود الخاصية قبل حذفها لتجنب الأخطاء
    if hasattr(bpy.types.Scene, 'selected_particle_system'):
        del bpy.types.Scene.selected_particle_system



if __name__ == "__main__":
    try:
        register()
    except:
        pass
    unregister() 


