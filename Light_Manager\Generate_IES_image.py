import os
import numpy as np
import matplotlib.pyplot as plt
from tkinter import Tk, filedialog

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
import ctypes




def read_ies_file(filepath):
    """قراءة زوايا رأسية وأول مجموعة إضاءة من ملف IES"""
    with open(filepath, 'r') as f:
        lines = f.readlines()

    # تجاوز الهيدر
    data_start = 0
    for i, line in enumerate(lines):
        if line.strip().startswith("TILT"):
            data_start = i + 1
            break

    # قراءة البيانات الرقمية
    data = []
    for line in lines[data_start:]:
        line = line.strip()
        if line:
            data.extend(line.split())

    data = list(map(float, data))

    try:
        num_lamps = int(data[0])
        lumens_per_lamp = float(data[1])
        candela_multiplier = float(data[2])
        num_vertical_angles = int(data[3])
        num_horizontal_angles = int(data[4])
        photometric_type = int(data[5])
        units_type = int(data[6])

        vertical_angles = data[7:7+num_vertical_angles]
        horizontal_angles = data[7+num_vertical_angles:7+num_vertical_angles+num_horizontal_angles]

        candela_start = 7 + num_vertical_angles + num_horizontal_angles
        total_candela = num_vertical_angles * num_horizontal_angles
        candela_values = data[candela_start:candela_start+total_candela]

        # نأخذ فقط أول شريحة من التوزيع
        slice_values = candela_values[:num_vertical_angles]

        return vertical_angles, slice_values
    except Exception as e:
        print(f"❌ خطأ أثناء قراءة الملف {filepath}: {e}")
        return None, None


def render_ies_to_image(angles_deg, candelas, save_path, width=400, height=600, scale_factor=3):
    angles = np.radians(angles_deg)
    candelas = np.array(candelas)

    if candelas.max() == 0:
        print("⚠️ كل قيم الشموع صفر. تجاهل الملف.")
        return

    candelas /= candelas.max()

    img = np.zeros((height, width))

    light_x = width // 2
    light_y = int(height * 0.1)  # نقطة مصدر الضوء 10% من الأعلى

    max_radius = min(width, height) * 0.9  # نصف القطر يعتمد على أصغر البعدين عشان ما يطلع برا

    for y in range(height):
        for x in range(width):
            dx = x - light_x
            dy = y - light_y
            r = np.sqrt(dx ** 2 + dy ** 2)

            if r == 0 or r > max_radius:
                continue

            theta = np.arccos(dy / r)
            if dx < 0:
                theta = 2 * np.pi - theta

            deg = np.degrees(theta)
            if deg > 180:
                deg = 360 - deg

            idx = np.searchsorted(angles_deg, deg)
            if idx == 0:
                intensity = candelas[0]
            elif idx >= len(angles_deg):
                intensity = candelas[-1]
            else:
                a0, a1 = angles_deg[idx - 1], angles_deg[idx]
                c0, c1 = candelas[idx - 1], candelas[idx]
                intensity = c0 + (c1 - c0) * ((deg - a0) / (a1 - a0))

            intensity = intensity * scale_factor
            if intensity > 1.0:
                intensity = 1

            falloff = (1 - r / max_radius) ** 2
            img[y, x] = intensity * falloff

    from matplotlib.colors import LinearSegmentedColormap
    colors = [(0, 0, 0), (1.0, 0.65, 0.39)]  # أسود لـ أصفر 4000K
    cmap = LinearSegmentedColormap.from_list("warm_yellow", colors, N=256)

    plt.imsave(save_path, img, cmap=cmap)
    
def show_message(title, text):
    ctypes.windll.user32.MessageBoxW(0, text, title, 0x40)

def main():
    root = Tk()
    root.withdraw()
    folder = filedialog.askdirectory(title="اختر مجلد ملفات IES")

    if not folder:
        print("❌ لم يتم اختيار مجلد.")
        return

    # output_dir = os.path.join(folder, "render_output")
    # os.makedirs(output_dir, exist_ok=True)


    for file in os.listdir(folder):
        if file.lower().endswith(".ies"):
            path = os.path.join(folder, file)
            angles, candelas = read_ies_file(path)
            if angles and candelas:
                #out_img = os.path.join(output_dir, os.path.splitext(file)[0] + ".png")
                out_img = os.path.join(folder, os.path.splitext(file)[0] + ".png")
                render_ies_to_image(angles, candelas, out_img)
                print(f"✔ تم توليد صورة لرندر: {out_img}")
            else:
                print(f"⚠ تم تخطي الملف: {file}")

    print("\n🎉 اكتملت عملية توليد صور الإضاءة!")
    show_message("نجاح", f"🎉 اكتملت عملية توليد صور الإضاءة!")


if __name__ == "__main__":
    main()
